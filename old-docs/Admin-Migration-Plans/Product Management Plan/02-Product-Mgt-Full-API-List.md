# Product Management API Reference

## **Overview**

This document provides a comprehensive reference for all Product Management API endpoints implemented in the staff app. All endpoints require staff authentication and appropriate role-based permissions.

**Base URL**: `/api/staff/products/`

## **Authentication**

All endpoints require:
- Valid JWT token in Authorization header: `Authorization: Bearer <token>`
- User must have `is_staff=True`
- User must have appropriate permissions for the specific operation

## **Response Format**

### **Success Response**
```json
{
  "success": true,
  "data": {...},
  "meta": {
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total_count": 150,
      "total_pages": 8,
      "has_next": true,
      "has_previous": false
    }
  }
}
```

### **Error Response**
```json
{
  "success": false,
  "errors": {
    "field_name": ["Error message"],
    "non_field_errors": ["General error"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

## **Product Management APIs**

### **Product CRUD Operations**

#### **List Products**
- **GET** `/api/staff/products/`
- **Permissions**: `products.view_product`
- **Description**: Get paginated list of products with filtering and search

**Query Parameters:**
- `page` (int): Page number
- `page_size` (int): Items per page (max 100)
- `search` (string): Search in title, description, brand, category
- `brand` (int[]): Filter by brand IDs
- `category` (int[]): Filter by category IDs
- `product_type` (int[]): Filter by product type IDs
- `is_active` (boolean): Filter by active status
- `is_digital` (boolean): Filter by digital status
- `created_after` (date): Filter by creation date
- `created_before` (date): Filter by creation date
- `min_rating` (float): Minimum average rating
- `max_rating` (float): Maximum average rating
- `has_stock` (boolean): Filter products with stock
- `low_stock` (boolean): Filter products with low stock (≤10)
- `out_of_stock` (boolean): Filter out of stock products
- `has_variants` (boolean): Filter products with variants
- `has_images` (boolean): Filter products with images
- `has_reviews` (boolean): Filter products with reviews
- `ordering` (string): Sort by field (title, created_at, updated_at, average_rating)

**Response Example:**
```json
{
  "results": [
    {
      "id": 1,
      "title": "Gaming Laptop",
      "slug": "gaming-laptop",
      "brand_title": "TechBrand",
      "product_type_title": "Laptop",
      "category_title": "Electronics > Computers",
      "is_active": true,
      "average_rating": 4.5,
      "variants_count": 3,
      "total_stock": 25,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-20T14:45:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total_count": 150,
    "total_pages": 8,
    "has_next": true,
    "has_previous": false
  }
}
```

#### **Get Product Details**
- **GET** `/api/staff/products/{id}/`
- **Permissions**: `products.view_product`
- **Description**: Get detailed product information including variants, images, and reviews

**Response Example:**
```json
{
  "id": 1,
  "title": "Gaming Laptop",
  "slug": "gaming-laptop",
  "brand": 1,
  "brand_title": "TechBrand",
  "description": "High-performance gaming laptop",
  "is_digital": false,
  "is_active": true,
  "product_type": 1,
  "product_type_title": "Laptop",
  "category": 1,
  "category_title": "Electronics > Computers",
  "average_rating": 4.5,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-20T14:45:00Z",
  "variants": [
    {
      "id": 1,
      "price": "1299.99",
      "price_label_title": "16GB RAM",
      "sku": "LAPTOP-001-16GB",
      "stock_qty": 10,
      "is_active": true,
      "weight": "2500.00",
      "condition": "New",
      "order": 1,
      "images": [
        {
          "id": 1,
          "alternative_text": "Front view",
          "image": "https://cloudinary.com/image1.jpg",
          "order": 1
        }
      ],
      "attribute_values": [
        {
          "id": 1,
          "attribute_value": "16GB",
          "attribute_title": "RAM"
        }
      ]
    }
  ],
  "reviews_count": 25,
  "total_stock": 25,
  "active_variants_count": 3
}
```

#### **Create Product**
- **POST** `/api/staff/products/`
- **Permissions**: `products.add_product`
- **Description**: Create a new product

**Request Body:**
```json
{
  "title": "New Gaming Laptop",
  "slug": "new-gaming-laptop",
  "brand": 1,
  "description": "Latest gaming laptop with RTX graphics",
  "is_digital": false,
  "is_active": true,
  "product_type": 1,
  "category": 1
}
```

#### **Update Product**
- **PUT/PATCH** `/api/staff/products/{id}/`
- **Permissions**: `products.change_product`
- **Description**: Update product information

**Request Body (PATCH):**
```json
{
  "title": "Updated Gaming Laptop",
  "description": "Updated description",
  "is_active": false
}
```

#### **Delete Product**
- **DELETE** `/api/staff/products/{id}/`
- **Permissions**: `products.delete_product`
- **Description**: Delete a product (soft delete recommended)

### **Product Bulk Operations**

#### **Bulk Operations**
- **POST** `/api/staff/products/bulk_operations/`
- **Permissions**: `products.bulk_update_products`
- **Description**: Perform bulk operations on multiple products

**Request Body:**
```json
{
  "operation_type": "activate",
  "product_ids": [1, 2, 3, 4, 5],
  "data": {
    "category_id": 2
  }
}
```

**Operation Types:**
- `update`: Bulk update fields
- `activate`: Set is_active=true
- `deactivate`: Set is_active=false
- `delete`: Delete products
- `assign_category`: Assign to category

#### **Create Product with Variants**
- **POST** `/api/staff/products/create_with_variants/`
- **Permissions**: `products.add_product`
- **Description**: Create product with variants in single transaction

**Request Body:**
```json
{
  "product": {
    "title": "Gaming Laptop Pro",
    "slug": "gaming-laptop-pro",
    "brand": 1,
    "category": 1,
    "product_type": 1,
    "description": "Professional gaming laptop"
  },
  "variants": [
    {
      "price": "1299.99",
      "sku": "LAPTOP-PRO-16GB",
      "stock_qty": 10,
      "condition": "New",
      "weight": "2500.00"
    },
    {
      "price": "1599.99",
      "sku": "LAPTOP-PRO-32GB",
      "stock_qty": 5,
      "condition": "New",
      "weight": "2500.00"
    }
  ]
}
```

#### **Change Product Status**
- **PATCH** `/api/staff/products/{id}/change_status/`
- **Permissions**: `products.change_product_status`
- **Description**: Change product active status

**Request Body:**
```json
{
  "is_active": false
}
```

#### **Product Analytics**
- **GET** `/api/staff/products/analytics/`
- **Permissions**: `products.view_product`
- **Description**: Get product analytics and statistics

**Response Example:**
```json
{
  "total_products": 150,
  "active_products": 142,
  "products_with_variants": 135,
  "products_with_reviews": 89,
  "average_rating": 4.2,
  "top_categories": [
    {
      "title": "Electronics",
      "products_count": 45
    }
  ],
  "top_brands": [
    {
      "title": "TechBrand",
      "products_count": 32
    }
  ],
  "stock_alerts": [
    {
      "product__title": "Gaming Mouse",
      "sku": "MOUSE-001",
      "stock_qty": 5
    }
  ]
}
```

## **Category Management APIs**

### **Category CRUD Operations**

#### **List Categories**
- **GET** `/api/staff/categories/`
- **Permissions**: `products.view_category`
- **Description**: Get hierarchical list of categories

**Query Parameters:**
- `search` (string): Search in title
- `is_active` (boolean): Filter by active status
- `parent` (int): Filter by parent category
- `level` (int): Filter by tree level
- `has_children` (boolean): Filter categories with children
- `has_products` (boolean): Filter categories with products

#### **Get Category Tree**
- **GET** `/api/staff/categories/tree/`
- **Permissions**: `products.view_category`
- **Description**: Get complete category tree structure

#### **Create Category**
- **POST** `/api/staff/categories/`
- **Permissions**: `products.add_category`

**Request Body:**
```json
{
  "title": "New Category",
  "slug": "new-category",
  "is_active": true,
  "parent": 1
}
```

#### **Move Category**
- **POST** `/api/staff/categories/{id}/move/`
- **Permissions**: `products.move_category`
- **Description**: Move category to new parent

**Request Body:**
```json
{
  "new_parent_id": 2
}
```

## **Brand Management APIs**

#### **List Brands**
- **GET** `/api/staff/brands/`
- **Permissions**: `products.view_brand`

**Query Parameters:**
- `search` (string): Search in title
- `is_active` (boolean): Filter by active status
- `has_products` (boolean): Filter brands with products
- `product_types` (int[]): Filter by product types

#### **Create Brand**
- **POST** `/api/staff/brands/`
- **Permissions**: `products.add_brand`

**Request Body:**
```json
{
  "title": "New Brand",
  "slug": "new-brand",
  "is_active": true,
  "product_types": [1, 2, 3]
}
```

## **Product Type Management APIs**

#### **List Product Types**
- **GET** `/api/staff/product-types/`
- **Permissions**: `products.view_producttype`

#### **Get Product Type Attributes**
- **GET** `/api/staff/product-types/{id}/attributes/`
- **Permissions**: `products.view_producttype`
- **Description**: Get attributes associated with product type

#### **Associate Attributes**
- **POST** `/api/staff/product-types/{id}/associate_attributes/`
- **Permissions**: `products.manage_type_attributes`
- **Description**: Bulk associate attributes with product type

**Request Body:**
```json
{
  "product_type_id": 1,
  "attributes": [
    {
      "attribute_id": 1,
      "is_filterable": true,
      "is_option_selector": false
    },
    {
      "attribute_id": 2,
      "is_filterable": false,
      "is_option_selector": true
    }
  ]
}
```

## **Attribute Management APIs**

#### **List Attributes**
- **GET** `/api/staff/attributes/`
- **Permissions**: `products.view_attribute`

**Query Parameters:**
- `search` (string): Search in title
- `has_values` (boolean): Filter attributes with values
- `product_types` (int[]): Filter by product types

#### **List Attribute Values**
- **GET** `/api/staff/attribute-values/`
- **Permissions**: `products.view_attribute`

**Query Parameters:**
- `search` (string): Search in attribute_value
- `attribute` (int): Filter by attribute
- `is_active` (boolean): Filter by active status
- `has_products` (boolean): Filter values used in products

#### **Bulk Create Attribute Values**
- **POST** `/api/staff/attribute-values/bulk_create/`
- **Permissions**: `products.add_attribute`

**Request Body:**
```json
{
  "attribute_id": 1,
  "values": ["Red", "Blue", "Green", "Yellow"]
}
```

## **Product Variant Management APIs**

#### **List Product Variants**
- **GET** `/api/staff/variants/`
- **Permissions**: `products.view_product`

**Query Parameters:**
- `product` (int): Filter by product
- `search` (string): Search in SKU, product title
- `is_active` (boolean): Filter by active status
- `condition` (string): Filter by condition
- `min_price` (decimal): Minimum price
- `max_price` (decimal): Maximum price
- `min_stock` (int): Minimum stock
- `max_stock` (int): Maximum stock
- `out_of_stock` (boolean): Filter out of stock variants
- `low_stock` (boolean): Filter low stock variants

#### **Update Stock**
- **PATCH** `/api/staff/variants/{id}/update_stock/`
- **Permissions**: `products.manage_product_variants`
- **Description**: Update variant stock quantity (for inventory managers)

**Request Body:**
```json
{
  "stock_qty": 25
}
```

## **Product Image Management APIs**

#### **List Product Images**
- **GET** `/api/staff/images/`
- **Permissions**: `products.view_product`

#### **Upload Product Image**
- **POST** `/api/staff/images/`
- **Permissions**: `products.manage_product_images`

**Request Body (multipart/form-data):**
```
alternative_text: "Product front view"
image: [file]
product_variant: 1
```

## **Review Management APIs**

#### **List Reviews**
- **GET** `/api/staff/reviews/`
- **Permissions**: `products.view_review`

**Query Parameters:**
- `product` (int): Filter by product
- `rating` (int): Filter by rating
- `min_rating` (int): Minimum rating
- `max_rating` (int): Maximum rating
- `posted_after` (date): Filter by post date
- `posted_before` (date): Filter by post date
- `search` (string): Search in title, description

#### **Moderate Review**
- **POST** `/api/staff/reviews/{id}/moderate/`
- **Permissions**: `products.moderate_review`

**Request Body:**
```json
{
  "action": "approve"
}
```

## **Discount Management APIs**

#### **List Discounts**
- **GET** `/api/staff/discounts/`
- **Permissions**: `products.view_discount`

**Query Parameters:**
- `search` (string): Search in name
- `is_active` (boolean): Filter by active status
- `is_valid` (boolean): Filter currently valid discounts
- `start_after` (date): Filter by start date
- `end_before` (date): Filter by end date
- `min_discount` (decimal): Minimum discount percentage
- `max_discount` (decimal): Maximum discount percentage

#### **Apply Discount to Variants**
- **POST** `/api/staff/discounts/{id}/apply_to_variants/`
- **Permissions**: `products.apply_discount`

**Request Body:**
```json
{
  "variant_ids": [1, 2, 3, 4, 5]
}
```

## **Audit and Operations APIs**

#### **Product Audit Logs**
- **GET** `/api/staff/audit/`
- **Permissions**: `products.view_product`

**Query Parameters:**
- `product_id` (int): Filter by product
- `staff_user_id` (int): Filter by staff user
- `action` (string): Filter by action type

#### **Bulk Operations Status**
- **GET** `/api/staff/bulk-operations/`
- **Permissions**: `products.view_product`

**Query Parameters:**
- `staff_user_id` (int): Filter by staff user
- `status` (string): Filter by operation status

## **Association Management APIs**

#### **Product Type Attributes**
- **GET** `/api/staff/associations/product_type_attributes/`
- **Permissions**: `products.view_attribute`

**Query Parameters:**
- `product_type_id` (int): Filter by product type
- `search` (string): Search in product type or attribute title

#### **Save Association**
- **POST** `/api/staff/associations/save_association/`
- **Permissions**: `products.manage_attribute_associations`

**Request Body:**
```json
{
  "product_type": 1,
  "attribute": 1,
  "is_filterable": true,
  "is_option_selector": false
}
```

#### **Delete Association**
- **DELETE** `/api/staff/associations/delete_association/`
- **Permissions**: `products.manage_attribute_associations`

**Request Body:**
```json
{
  "association_id": 1
}
```

## **Error Codes**

| Code | Description |
|------|-------------|
| `AUTHENTICATION_REQUIRED` | User not authenticated |
| `PERMISSION_DENIED` | Insufficient permissions |
| `VALIDATION_ERROR` | Invalid input data |
| `NOT_FOUND` | Resource not found |
| `BULK_OPERATION_FAILED` | Bulk operation encountered errors |
| `ASSOCIATION_EXISTS` | Association already exists |
| `CIRCULAR_DEPENDENCY` | Circular reference detected |

## **Rate Limiting**

- **Standard endpoints**: 100 requests per minute per user
- **Bulk operations**: 10 requests per minute per user
- **File uploads**: 20 requests per minute per user

## **Pagination**

All list endpoints support pagination:
- Default page size: 20
- Maximum page size: 100
- Use `page` and `page_size` query parameters

---

**Document Version**: 1.0
**Last Updated**: 2025-07-04
**API Version**: v1