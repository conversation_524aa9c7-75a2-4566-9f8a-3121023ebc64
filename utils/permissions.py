from rest_framework import permissions
from rest_framework.exceptions import PermissionDenied
import logging

logger = logging.getLogger(__name__)


class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        logger.debug("IsAdminOrReadOnly permission check")
        # if not request.user.is_authenticated:
        #     logger.warning("User not authenticated")
        #     print("IsAdminOrReadOnly called")
        #     return False
        print("User authenticated")
        if request.method in permissions.SAFE_METHODS:
            print("User authenticated")
            return True
        return bool(request.user and request.user.is_staff)


# Prevent unauthorized users from accessing data
class FullDjangoModelPermissions(permissions.DjangoModelPermissions):
    def __init__(self) -> None:
        self.perms_map['GET'] = ['%(app_label)s.view_%(model_name)s']


# class ViewCustomerHistoryPermission(permissions.BasePermission):
#     def has_permission(self, request, view):
#         return request.user.has_perm('store.view_history')


class IsAdminOrGroupMember(permissions.BasePermission):

    def has_permission(self, request, view):
        """
        This method checks if the user is authenticated.
        It is the first level of permission check and applies to the entire view.
        """
        return request.user.is_authenticated  # Allow only authenticated users to access the view.

    def has_object_permission(self, request, view, obj):
        if request.user.is_staff or request.user.is_superuser:
            return True

        user = request.user
        # # Handle GET requests
        if request.method == 'GET':
            return (user.is_staff or
                    obj.customer == user.customer or
                    user.has_perm('store.view_order'))

        # Handle PATCH requests (partial updates to an object).
        if request.method == 'PATCH':
            # Ensure the user is in one of the specified groups
            user_groups = request.user.groups.filter(name__in=['LogisticsCoordinators', 'OrderVerifiers']).values_list(
                'name', flat=True)

            # Check if user is an OrderVerifier and trying to set status to 'Processing'
            if 'OrderVerifiers' in user_groups and request.data.get('delivery_status') == 'Processing':
                return True

            # Check if user is a LogisticsCoordinator and trying to set status to 'Dispatched'
            if 'LogisticsCoordinators' in user_groups and request.data.get('delivery_status') == 'Dispatched':
                return True

            # If the user is neither trying to set the allowed status or isn't in the allowed group, deny access
            return False

        # Handle DELETE requests.
        if request.method == 'DELETE':
            if request.user.is_staff and (obj.delivery_status == 'Shipped' or obj.delivery_status == 'Delivered'):
                return True
            if obj.customer == request.user.customer:
                if obj.delivery_status == 'Pending':
                    return True
                else:
                    raise PermissionDenied("You can only delete orders with 'Pending' delivery status.")
            return False

        # If none of the conditions above are met, deny access by default.
        return False


class IsAdminOrOwner(permissions.BasePermission):
    def has_permission(self, request, view):
        # Allow only authenticated users
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admins can access any object
        if request.user.is_staff or request.user.is_superuser:
            return True
        # Regular users can only access their own profile
        return (obj.user or obj.customer.user) == request.user


class IsAdminOrCustomer(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Admins can access any object
        if request.user.is_staff or request.user.is_superuser:
            return True
        # Regular users can only access their own profile
        return obj.customer.user == request.user
