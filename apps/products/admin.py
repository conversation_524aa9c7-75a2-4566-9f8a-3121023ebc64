from django.contrib import admin
from django.urls import reverse
from django.utils.safestring import mark_safe
from django import forms
from django.contrib import messages
from django.shortcuts import render, redirect
from django.urls import path
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json
from mptt.admin import DraggableMPTTAdmin
from ordered_model.admin import OrderedTabularInline, OrderedModelAdmin
from .forms import ProductVariantForm, ProductTypeForm, AttributeForm, CategoryForm, ProductImageForm, ProductTypeSelectionForm, ProductTypeAttributeRowForm, AttributeSelectionForm, AttributeValueRowForm
from .models import (Category, Product, ProductVariant, ProductImage, AttributeValue, Attribute, ProductTypeAttribute,
                     ProductType, Review, ProductVariantAttributeValue,
                     Brand, BrandProductType, Discount)


class EditLinkInline(object):
    def edit(self, instance):
        url = reverse(
            f'admin:{instance._meta.app_label}_{instance._meta.model_name}_change',
            args=[instance.pk]
        )
        if instance.pk:
            link = mark_safe('<a href="{u}">edit</a>'.format(u=url))
            return link
        else:
            return ''


@admin.register(Category)
class CategoryAdmin(DraggableMPTTAdmin):
    form = CategoryForm
    mptt_indent_field = 'title'
    list_display = ['id', 'tree_actions', 'indented_title', 'related_products_count',
                    'related_products_cumulative_count', 'slug', 'is_active']
    search_fields = ['title']
    list_display_links = ['indented_title']
    autocomplete_fields = ['parent']

    def get_queryset(self, request):
        qs = super().get_queryset(request)

        # Add cumulative product count
        qs = Category.objects.add_related_count(
            qs,
            Product,
            'category',
            'products_cumulative_count',
            cumulative=True)

        # Add non cumulative product count
        qs = Category.objects.add_related_count(qs,
                                                Product,
                                                'category',
                                                'products_count',
                                                cumulative=False)
        return qs

    def related_products_count(self, instance):
        return instance.products_count

    related_products_count.short_description = 'Related products (for this specific category)'

    def related_products_cumulative_count(self, instance):
        return instance.products_cumulative_count

    related_products_cumulative_count.short_description = 'Related products (in tree)'


class ProductImageInline(admin.TabularInline):
    list_display = ['alternative_text', 'image', 'order']
    readonly_fields = ['order']
    model = ProductImage
    form = ProductImageForm
    extra = 1

    # Fields to display in the inline
    # Include product_variant but we'll hide it with CSS
    fields = ['alternative_text', 'image', 'product_variant', 'order']

    # Hide the default delete checkbox
    can_delete = False

    # Add a template to customize the inline
    template = 'admin/products/productvariant/edit_inline/tabular.html'

    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        # Set the product_variant automatically when adding a new image
        if obj and 'product_variant' in formset.form.base_fields:
            formset.form.base_fields['product_variant'].initial = obj
            # Make it a hidden field
            formset.form.base_fields['product_variant'].widget = forms.HiddenInput()
        return formset


# class ProductVariantInline(EditLinkInline, admin.TabularInline):
#     form = ProductVariantForm
#     model = ProductVariant
#     autocomplete_fields = ['price_label']
#     readonly_fields = ['edit', 'order']
#     min_num = 1  # Minimum number of product variant instances
#     extra = 1

class ProductVariantInline(EditLinkInline, OrderedTabularInline):
    model = ProductVariant
    form = ProductVariantForm
    fields = ('drag_handle', 'price', 'price_label', 'sku', 'stock_qty', 'condition', 'order', 'edit')
    readonly_fields = ('drag_handle', 'order', 'edit')
    ordering = ('price',)
    autocomplete_fields = ['price_label']
    extra = 1
    template = 'admin/products/productvariant_inline/tabular.html'

    def drag_handle(self, obj):
        """Provide drag handle for reordering"""
        return '⋮⋮'

    drag_handle.short_description = '⋮⋮'

    class Media:
        css = {
            'all': ('admin/css/sortable-tabular-inline.css',)
        }
        js = (
            'admin/js/vendor/sortable.min.js',
            'admin/js/sortable-tabular-inline.js',
        )


class AttributeInline(admin.TabularInline):
    model = Attribute.product_type_attribute.through


class AttributeValueInline(admin.TabularInline):
    model = AttributeValue.product_variant_attribute_value.through
    autocomplete_fields = ['attribute_value']
    template = 'admin/products/attributevalue/edit_inline/tabular.html'
    extra = 1


class AttributeValueProductInline(admin.TabularInline):
    model = AttributeValue.product_attr_value.through


class ReviewInline(admin.TabularInline):
    model = Review
    extra = 0
    can_delete = False
    max_num = 0  # Prevents adding new reviews
    show_change_link = True
    readonly_fields = ['product', 'customer', 'title', 'description', 'rating', 'posted_at']


"""
When you include foreign key fields in list_display,
Django tries to access these related objects for each row in the admin list view.
If there are a large number of Review objects, or if the related Product
or Customer queries are slow, this can cause significant performance issues
or even timeout errors, resulting in an empty table.
"""


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'get_product', 'get_customer', 'rating', 'posted_at']
    list_filter = ['product', 'posted_at']
    search_fields = ['title', 'description']
    readonly_fields = ['product', 'customer', 'title', 'description', 'rating', 'posted_at']

    # Disable add/delete permissions
    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_product(self, obj):
        return obj.product.title

    def get_customer(self, obj):
        return f"{obj.customer.user.email}"

    get_product.short_description = 'Product'
    get_customer.short_description = 'Customer'


@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'is_active']
    ordering = ['id', 'title', 'is_active']
    search_fields = ['title']
    list_filter = ['is_active']
    prepopulated_fields = {
        'slug': ['title']
    }


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'is_digital', 'is_active', 'product_type', 'category',
                    'updated_at', 'created_at']
    ordering = ['id', 'title', 'is_digital', 'is_active', 'product_type', 'category',
                'updated_at', 'created_at']
    list_filter = ['product_type', 'category']
    search_fields = ['title', 'category']
    autocomplete_fields = ['product_type', 'category']
    readonly_fields = ['average_rating']
    prepopulated_fields = {
        'slug': ['title']
    }
    inlines = [
        ProductVariantInline,
        # AttributeValueProductInline,
        ReviewInline,
    ]

    # class Media:
    #     css = {
    #         'all': (
    #             'admin/css/sortable-tabular-inline.css',
    #             'css/admin_custom.css',
    #         )
    #     }
    #     js = (
    #         'admin/js/vendor/sortable.min.js',
    #         'admin/js/sortable-tabular-inline.js',
    #     )


@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    form = ProductVariantForm
    list_display = ['id', 'price_label', 'product', 'price', 'sku', 'stock_qty', 'is_active',
                    'weight', 'condition', 'created_at', 'updated_at']
    ordering = ['order']
    list_filter = ['is_active', 'stock_qty']
    search_fields = ['product__title', 'price_label']
    autocomplete_fields = ['product', 'price_label']
    readonly_fields = ['order']
    inlines = [
        ProductImageInline,
        AttributeValueInline,
    ]

    # Use our custom template for the change form
    change_form_template = 'admin/products/productvariant/change_form.html'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj is None:  # This is a creation form
            form.base_fields['price_label'].widget.attrs['disabled'] = 'disabled'
        return form

    def save_model(self, request, obj, form, change):
        if not change:  # This is a new instance
            obj.price_label = None  # Ensure price_label is not set for new instances
        super().save_model(request, obj, form, change)

    def save_formset(self, request, form, formset, change):
        """Custom handling for inline formsets"""
        instances = formset.save(commit=False)

        # Handle new inline objects
        for instance in instances:
            # If this is a ProductImage instance and product_variant is not set
            if isinstance(instance, ProductImage) and not instance.product_variant_id:
                # Set the product_variant to the parent object
                instance.product_variant = form.instance
            instance.save()

        # Handle deleted inline objects
        for obj in formset.deleted_objects:
            obj.delete()

        formset.save_m2m()


@admin.register(ProductVariantAttributeValue)
class ProductVariantAttributeValueAdmin(admin.ModelAdmin):
    list_display = ['id', 'attribute_value', 'product_variant', 'is_active']
    ordering = ['id', 'attribute_value', 'product_variant', 'is_active']
    list_filter = ['is_active']
    search_fields = ['attribute_value', 'product_variant']
    autocomplete_fields = ['attribute_value', 'product_variant']
    extra = 1


@admin.register(Attribute)
class AttributeAdmin(admin.ModelAdmin):
    form = AttributeForm
    list_display = ['id', 'title']
    search_fields = ['id', 'title']
    list_filter = ['product_type_attribute']


class ProductTypeAttributeAdmin(admin.ModelAdmin):
    list_display = ['id', 'product_type', 'attribute', 'is_filterable', 'is_option_selector']
    ordering = ['id', 'product_type', 'attribute']
    search_fields = ['product_type', 'attribute']
    list_filter = ['product_type', 'attribute', 'is_filterable', 'is_option_selector']
    autocomplete_fields = ['product_type', 'attribute']


class EnhancedProductTypeAttributeAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for managing product type attributes with AJAX-powered
    individual row management, autocomplete, and real-time editing capabilities.
    """
    change_list_template = 'admin/products/enhanced_product_type_attribute_changelist.html'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('manage/', self.admin_site.admin_view(self.manage_view), name='products_enhanced_product_type_attribute_manage'),
            path('ajax/save-association/', self.admin_site.admin_view(self.ajax_save_association), name='products_ajax_save_association'),
            path('ajax/update-association/', self.admin_site.admin_view(self.ajax_update_association), name='products_ajax_update_association'),
            path('ajax/delete-association/', self.admin_site.admin_view(self.ajax_delete_association), name='products_ajax_delete_association'),
            path('ajax/load-associations/', self.admin_site.admin_view(self.ajax_load_associations), name='products_ajax_load_associations'),
            path('ajax/load-attributes/', self.admin_site.admin_view(self.ajax_load_attributes), name='products_ajax_load_attributes'),
        ]
        return custom_urls + urls

    def manage_view(self, request):
        """
        Main management interface for product type attributes.
        """
        product_type_form = ProductTypeSelectionForm()

        context = {
            'product_type_form': product_type_form,
            'title': 'Manage Product Type Attributes',
            'opts': ProductTypeAttribute._meta,
            'has_change_permission': True,
        }

        return render(request, 'admin/products/enhanced_product_type_attribute_manage.html', context)

    @method_decorator(csrf_exempt)
    def ajax_save_association(self, request):
        """
        AJAX endpoint to save a new product type attribute association.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                product_type_id = data.get('product_type_id')
                attribute_id = data.get('attribute_id')
                is_filterable = data.get('is_filterable', False)
                is_option_selector = data.get('is_option_selector', False)

                # Validate required fields
                if not product_type_id or not attribute_id:
                    return JsonResponse({'success': False, 'error': 'Missing required fields'})

                # Check if association already exists
                if ProductTypeAttribute.objects.filter(
                    product_type_id=product_type_id,
                    attribute_id=attribute_id
                ).exists():
                    return JsonResponse({'success': False, 'error': 'This attribute is already associated with this product type'})

                # Create the association
                association = ProductTypeAttribute.objects.create(
                    product_type_id=product_type_id,
                    attribute_id=attribute_id,
                    is_filterable=is_filterable,
                    is_option_selector=is_option_selector
                )

                return JsonResponse({
                    'success': True,
                    'association_id': association.id,
                    'attribute_name': association.attribute.title,
                    'message': f'Successfully associated {association.attribute.title} with {association.product_type.title}'
                })

            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    @method_decorator(csrf_exempt)
    def ajax_update_association(self, request):
        """
        AJAX endpoint to update an existing product type attribute association.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                association_id = data.get('association_id')
                is_filterable = data.get('is_filterable', False)
                is_option_selector = data.get('is_option_selector', False)

                if not association_id:
                    return JsonResponse({'success': False, 'error': 'Missing association ID'})

                # Update the association
                association = ProductTypeAttribute.objects.get(id=association_id)
                association.is_filterable = is_filterable
                association.is_option_selector = is_option_selector
                association.save()

                return JsonResponse({
                    'success': True,
                    'message': f'Successfully updated {association.attribute.title} settings'
                })

            except ProductTypeAttribute.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Association not found'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    @method_decorator(csrf_exempt)
    def ajax_delete_association(self, request):
        """
        AJAX endpoint to delete a product type attribute association.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                association_id = data.get('association_id')

                if not association_id:
                    return JsonResponse({'success': False, 'error': 'Missing association ID'})

                # Delete the association
                association = ProductTypeAttribute.objects.get(id=association_id)
                attribute_name = association.attribute.title
                association.delete()

                return JsonResponse({
                    'success': True,
                    'message': f'Successfully removed {attribute_name} association'
                })

            except ProductTypeAttribute.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Association not found'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    def ajax_load_associations(self, request):
        """
        AJAX endpoint to load existing associations for a product type.
        """
        if request.method == 'GET':
            try:
                product_type_id = request.GET.get('product_type_id')

                if not product_type_id:
                    return JsonResponse({'success': False, 'error': 'Missing product type ID'})

                # Get existing associations
                associations = ProductTypeAttribute.objects.filter(
                    product_type_id=product_type_id
                ).select_related('attribute')

                associations_data = []
                for assoc in associations:
                    associations_data.append({
                        'id': assoc.id,
                        'attribute_id': assoc.attribute.id,
                        'attribute_name': assoc.attribute.title,
                        'is_filterable': assoc.is_filterable,
                        'is_option_selector': assoc.is_option_selector
                    })

                return JsonResponse({
                    'success': True,
                    'associations': associations_data
                })

            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    def ajax_load_attributes(self, request):
        """
        AJAX endpoint to load available attributes for a product type.
        """
        if request.method == 'GET':
            try:
                product_type_id = request.GET.get('product_type_id')

                if not product_type_id:
                    return JsonResponse({'success': False, 'error': 'Missing product type ID'})

                # Get attributes not already associated with this product type
                associated_attribute_ids = ProductTypeAttribute.objects.filter(
                    product_type_id=product_type_id
                ).values_list('attribute_id', flat=True)

                available_attributes = Attribute.objects.exclude(
                    id__in=associated_attribute_ids
                ).order_by('title')

                attributes_data = []
                for attr in available_attributes:
                    attributes_data.append({
                        'id': attr.id,
                        'title': attr.title
                    })

                return JsonResponse({
                    'success': True,
                    'attributes': attributes_data
                })

            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    def changelist_view(self, request, extra_context=None):
        """
        Override changelist to redirect to our enhanced management interface.
        """
        return redirect('admin:products_enhanced_product_type_attribute_manage')

    def add_view(self, request, form_url='', extra_context=None):
        """
        Override add_view to redirect to our enhanced management interface.
        """
        return redirect('admin:products_enhanced_product_type_attribute_manage')

    def has_add_permission(self, request):
        """
        Always return True to show the Add button.
        """
        return True


# Create a proxy model for the regular admin interface
class ProductTypeAttributeProxy(ProductTypeAttribute):
    class Meta:
        proxy = True
        verbose_name = "Product Type Attribute (Individual)"
        verbose_name_plural = "Product Type Attributes (Individual)"


# Create a proxy model for the regular AttributeValue admin interface
class AttributeValueProxy(AttributeValue):
    class Meta:
        proxy = True
        verbose_name = "Attribute Value (Individual)"
        verbose_name_plural = "Attribute Values (Individual)"


# Register both admin interfaces
admin.site.register(ProductTypeAttribute, EnhancedProductTypeAttributeAdmin)
admin.site.register(ProductTypeAttributeProxy, ProductTypeAttributeAdmin)

# AttributeValue registration will be moved to the end of the file


class ProductTypeFilter(admin.SimpleListFilter):
    title = 'Product Type'
    parameter_name = 'product_type'

    def lookups(self, request, model_admin):
        product_types = ProductType.objects.all()
        return [(pt.id, pt.title) for pt in product_types]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(attribute__product_type_attribute__product_type__id=self.value()).distinct()
        return queryset


class AttributeValueAdmin(admin.ModelAdmin):
    list_display = ['id', 'title', 'attribute', 'get_product_type', 'is_active']
    ordering = ['id', 'attribute__title', 'attribute', 'is_active']
    search_fields = ['attribute_value', 'attribute__title']
    list_filter = [ProductTypeFilter, 'attribute__title', 'is_active']

    # list_select_related = ['attribute']
    autocomplete_fields = ['attribute']

    def get_product_type(self, obj):
        product_types = ProductType.objects.filter(attribute=obj.attribute).distinct()
        return ", ".join([pt.title for pt in product_types])

    get_product_type.short_description = "Product Type"


class EnhancedAttributeValueAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for managing attribute values with AJAX-powered
    individual row management and real-time editing capabilities.
    """
    change_list_template = 'admin/products/enhanced_attribute_value_changelist.html'
    search_fields = ['attribute_value', 'attribute__title']  # Required for autocomplete_fields

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('manage/', self.admin_site.admin_view(self.manage_view), name='products_enhanced_attribute_value_manage'),
            path('ajax/save-value/', self.admin_site.admin_view(self.ajax_save_value), name='products_ajax_save_value'),
            path('ajax/update-value/', self.admin_site.admin_view(self.ajax_update_value), name='products_ajax_update_value'),
            path('ajax/delete-value/', self.admin_site.admin_view(self.ajax_delete_value), name='products_ajax_delete_value'),
            path('ajax/load-values/', self.admin_site.admin_view(self.ajax_load_values), name='products_ajax_load_values'),
        ]
        return custom_urls + urls

    def manage_view(self, request):
        """
        Main management interface for attribute values.
        """
        attribute_form = AttributeSelectionForm()

        context = {
            'attribute_form': attribute_form,
            'title': 'Manage Attribute Values',
            'opts': AttributeValue._meta,
            'has_change_permission': True,
        }

        return render(request, 'admin/products/enhanced_attribute_value_manage.html', context)

    @method_decorator(csrf_exempt)
    def ajax_save_value(self, request):
        """
        AJAX endpoint to save a new attribute value.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                attribute_id = data.get('attribute_id')
                attribute_value = data.get('attribute_value')
                is_active = data.get('is_active', True)

                # Validate required fields
                if not attribute_id or not attribute_value:
                    return JsonResponse({'success': False, 'error': 'Missing required fields'})

                # Check if value already exists
                if AttributeValue.objects.filter(
                    attribute_id=attribute_id,
                    attribute_value__iexact=attribute_value
                ).exists():
                    return JsonResponse({'success': False, 'error': f'The value "{attribute_value}" already exists for this attribute'})

                # Create the value
                value = AttributeValue.objects.create(
                    attribute_id=attribute_id,
                    attribute_value=attribute_value,
                    is_active=is_active
                )

                return JsonResponse({
                    'success': True,
                    'value_id': value.id,
                    'value_display': value.attribute_value,
                    'message': f'Successfully created value "{value.attribute_value}"'
                })

            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    @method_decorator(csrf_exempt)
    def ajax_update_value(self, request):
        """
        AJAX endpoint to update an existing attribute value.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                value_id = data.get('value_id')
                attribute_value = data.get('attribute_value')
                is_active = data.get('is_active', True)

                if not value_id:
                    return JsonResponse({'success': False, 'error': 'Missing value ID'})

                # Update the value
                value = AttributeValue.objects.get(id=value_id)

                # Check for duplicates (excluding current value)
                if AttributeValue.objects.filter(
                    attribute=value.attribute,
                    attribute_value__iexact=attribute_value
                ).exclude(id=value_id).exists():
                    return JsonResponse({'success': False, 'error': f'The value "{attribute_value}" already exists for this attribute'})

                value.attribute_value = attribute_value
                value.is_active = is_active
                value.save()

                return JsonResponse({
                    'success': True,
                    'message': f'Successfully updated value to "{value.attribute_value}"'
                })

            except AttributeValue.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Value not found'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    @method_decorator(csrf_exempt)
    def ajax_delete_value(self, request):
        """
        AJAX endpoint to delete an attribute value.
        """
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
                value_id = data.get('value_id')

                if not value_id:
                    return JsonResponse({'success': False, 'error': 'Missing value ID'})

                # Delete the value
                value = AttributeValue.objects.get(id=value_id)
                value_name = value.attribute_value
                value.delete()

                return JsonResponse({
                    'success': True,
                    'message': f'Successfully deleted value "{value_name}"'
                })

            except AttributeValue.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Value not found'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    def ajax_load_values(self, request):
        """
        AJAX endpoint to load existing values for an attribute.
        """
        if request.method == 'GET':
            try:
                attribute_id = request.GET.get('attribute_id')

                if not attribute_id:
                    return JsonResponse({'success': False, 'error': 'Missing attribute ID'})

                # Get existing values
                values = AttributeValue.objects.filter(
                    attribute_id=attribute_id
                ).order_by('attribute_value')

                values_data = []
                for value in values:
                    values_data.append({
                        'id': value.id,
                        'attribute_value': value.attribute_value,
                        'is_active': value.is_active
                    })

                return JsonResponse({
                    'success': True,
                    'values': values_data
                })

            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)})

        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    def changelist_view(self, request, extra_context=None):
        """
        Override changelist to redirect to our enhanced management interface.
        """
        return redirect('admin:products_enhanced_attribute_value_manage')

    def add_view(self, request, form_url='', extra_context=None):
        """
        Override add_view to redirect to our enhanced management interface.
        """
        return redirect('admin:products_enhanced_attribute_value_manage')

    def has_add_permission(self, request):
        """
        Always return True to show the Add button.
        """
        return True


@admin.register(ProductType)
class ProductTypeMainAdmin(admin.ModelAdmin):
    form = ProductTypeForm
    list_display = ['id', 'title', 'parent']
    inlines = [
        AttributeInline
    ]
    search_fields = ['title']
    readonly_fields = ['parent']

    class Media:
        css = {
            'all': ('admin/css/widgets.css', 'css/admin_custom.css',)
        }


@admin.register(BrandProductType)
class BrandProductTypeAdmin(admin.ModelAdmin):
    list_display = ['id', 'brand', 'product_type']
    ordering = ['id', 'brand', 'product_type']
    search_fields = ['brand', 'product_type']
    list_filter = ['brand', 'product_type']
    autocomplete_fields = ['brand', 'product_type']


# @admin.register(Review)
# class ReviewAdmin(admin.ModelAdmin):
#     list_display = ['product', 'name', 'description', 'rating', 'posted_at']
#     list_filter = ['product', 'posted_at']
#     search_fields = ['name', 'description']

# Define the safely_delete_image function before using it
def safely_delete_image(modeladmin, request, queryset):
    for image in queryset:
        image.delete()


safely_delete_image.short_description = "Safely delete selected images"


# Register ProductImage directly with admin
@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    form = ProductImageForm
    list_display = ['id', 'alternative_text', 'get_product_variant', 'order']
    list_filter = ['product_variant__product__title']
    search_fields = ['alternative_text', 'product_variant__product__title']
    readonly_fields = ['order']
    actions = [safely_delete_image]

    def get_product_variant(self, obj):
        return f"{obj.product_variant.product.title} - {obj.product_variant.sku}"

    get_product_variant.short_description = 'Product Variant'


@admin.register(Discount)
class DiscountAdmin(admin.ModelAdmin):
    list_display = (
        'name',
        'discount_percentage',
        'start_date',
        'end_date',
        'is_active',
        'get_product_variants',
        'is_valid_status',
    )
    list_filter = ('is_active', 'start_date', 'end_date')  # Filters for quick access
    search_fields = ('name',)  # Allow search by discount name
    ordering = ('-start_date',)  # Order discounts by start date, newest first
    filter_horizontal = ('product_variants',)  # For ManyToManyField UI

    def get_product_variants(self, obj):
        """Display linked product variants."""
        return ", ".join([pv.sku for pv in obj.product_variants.all()])

    get_product_variants.short_description = 'Linked Product Variants'

    def is_valid_status(self, obj):
        """Show if the discount is currently valid."""
        return obj.is_valid()

    is_valid_status.boolean = True
    is_valid_status.short_description = 'Currently Valid'


# Register AttributeValue with enhanced admin and proxy with regular admin
admin.site.register(AttributeValue, EnhancedAttributeValueAdmin)
admin.site.register(AttributeValueProxy, AttributeValueAdmin)
