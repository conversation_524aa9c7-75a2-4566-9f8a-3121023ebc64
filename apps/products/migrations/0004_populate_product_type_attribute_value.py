from django.db import migrations

def forward(apps, schema_editor):
    AttributeValue = apps.get_model('products', 'AttributeValue')
    ProductType = apps.get_model('products', 'ProductType')
    ProductTypeAttribute = apps.get_model('products', 'ProductTypeAttribute')
    ProductTypeAttributeValue = apps.get_model('products', 'ProductTypeAttributeValue')

    # For each product type and its attributes
    for pta in ProductTypeAttribute.objects.all():
        product_type = pta.product_type
        attribute = pta.attribute

        # Get all attribute values for this attribute
        attribute_values = AttributeValue.objects.filter(attribute=attribute)

        # Create ProductTypeAttributeValue entries
        for av in attribute_values:
            # Check if the attribute value has the old fields
            selectable = False
            for_filtering = True

            # Try to get the old values if they exist
            if hasattr(av, 'selectable'):
                selectable = av.selectable
            if hasattr(av, 'for_filtering'):
                for_filtering = av.for_filtering

            # Create the new entry
            ProductTypeAttributeValue.objects.get_or_create(
                product_type=product_type,
                attribute_value=av,
                defaults={
                    'selectable': selectable,
                    'for_filtering': for_filtering
                }
            )

def backward(apps, schema_editor):
    # No need to implement backward migration as it would be complex
    # and potentially destructive
    pass

class Migration(migrations.Migration):
    dependencies = [
        ('products', '0003_remove_product_attribute_value_and_more'),
    ]

    operations = [
        migrations.RunPython(forward, backward),
    ]
