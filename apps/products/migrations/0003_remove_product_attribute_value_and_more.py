# Generated by Django 5.1.6 on 2025-05-12 12:23

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='product',
            name='attribute_value',
        ),
        migrations.RemoveField(
            model_name='attributevalue',
            name='for_filtering',
        ),
        migrations.RemoveField(
            model_name='attributevalue',
            name='selectable',
        ),
        migrations.AlterField(
            model_name='productvariant',
            name='weight',
            field=models.DecimalField(decimal_places=2, help_text='Please add the weight in grams.', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))]),
        ),
        migrations.CreateModel(
            name='ProductTypeAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('selectable', models.BooleanField(default=False)),
                ('for_filtering', models.BooleanField(default=True)),
                ('attribute_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_type_attribute_values', to='products.attributevalue')),
                ('product_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_type_attribute_values', to='products.producttype')),
            ],
            options={
                'verbose_name': 'Product Type Attribute Value',
                'unique_together': {('product_type', 'attribute_value')},
            },
        ),
        migrations.DeleteModel(
            name='ProductAttributeValue',
        ),
    ]
