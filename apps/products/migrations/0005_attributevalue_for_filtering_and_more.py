# Generated by Django 5.1.6 on 2025-05-12 15:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0004_populate_product_type_attribute_value'),
    ]

    operations = [
        migrations.AddField(
            model_name='attributevalue',
            name='for_filtering',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='attributevalue',
            name='selectable',
            field=models.BooleanField(default=False),
        ),
        migrations.DeleteModel(
            name='ProductTypeAttributeValue',
        ),
    ]
