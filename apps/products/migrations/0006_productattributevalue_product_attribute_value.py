# Generated by Django 5.1.6 on 2025-05-13 06:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0005_attributevalue_for_filtering_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductAttributeValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attribute_value', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_value_av', to='products.attributevalue')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_value_pv', to='products.product')),
            ],
            options={
                'unique_together': {('attribute_value', 'product')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='attribute_value',
            field=models.ManyToManyField(blank=True, related_name='product_attr_value', through='products.ProductAttributeValue', to='products.attributevalue'),
        ),
    ]
