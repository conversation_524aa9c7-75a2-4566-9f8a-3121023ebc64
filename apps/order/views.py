from django.db.models import Prefetch
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from utils.permissions import IsAdminOrGroupMember
from utils.pagination import DefaultPagination
from .models import Order, OrderItem
from ..customers.models import Customer
from .serializers import OrderSerializer, UpdateOrderDeliveryStatusSerializer, CreateOrderSerializer


class OrderViewSet(ModelViewSet):
    http_method_names = ['get', 'post', 'delete', 'patch', 'head', 'options']
    permission_classes = [IsAdminOrGroupMember]
    pagination_class = DefaultPagination

    def get_queryset(self):
        user = self.request.user
        base_queryset = Order.objects.select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related(
            Prefetch(
                'ordered_items',
                queryset=OrderItem.objects.select_related('product', 'product_variant')
                .prefetch_related(
                    'product_variant__price_label',
                    'product_variant__product_image'
                )
            )
        )

        # .distinct() is a QuerySet method used to remove duplicate rows from the result set of a query
        user_groups = set(user.groups.values_list('name', flat=True))
        if user.is_staff or user.is_superuser:
            return base_queryset.distinct()
        if 'OrderVerifiers' in user_groups:
            return base_queryset.filter(delivery_status='Pending').distinct()
        if 'LogisticsCoordinators' in user_groups:
            return base_queryset.filter(delivery_status='Processing').distinct()
        return base_queryset.filter(customer__user=user).order_by('-placed_at').distinct()

    def create(self, request, *args, **kwargs):
        serializer = CreateOrderSerializer(data=request.data, context={'user_id': request.user.id})
        serializer.is_valid(raise_exception=True)
        order = serializer.save()
        return Response(OrderSerializer(order).data, status=status.HTTP_201_CREATED)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CreateOrderSerializer
        elif self.request.method == 'PATCH':
            return UpdateOrderDeliveryStatusSerializer
        return OrderSerializer
