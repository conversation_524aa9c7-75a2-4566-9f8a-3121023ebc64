# Generated by Django 5.1.6 on 2025-07-02 21:10

import django.contrib.auth.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('staff', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(help_text='Unique employee identifier', max_length=20, unique=True)),
                ('department', models.CharField(choices=[('PRODUCT', 'Product Management'), ('ORDER', 'Order Management'), ('CUSTOMER', 'Customer Management'), ('CONTENT', 'Content Management'), ('FINANCE', 'Finance & Analytics'), ('ADMIN', 'Administration'), ('IT', 'Information Technology')], help_text='Primary department', max_length=20)),
                ('position_title', models.CharField(help_text='Official job title', max_length=100)),
                ('hire_date', models.DateField(help_text='Date of hire')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('ON_LEAVE', 'On Leave'), ('TERMINATED', 'Terminated')], default='ACTIVE', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Administrative notes about the staff member')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('auth.group',),
            managers=[
                ('objects', django.contrib.auth.models.GroupManager()),
            ],
        ),
        migrations.AlterModelOptions(
            name='groupmembership',
            options={'ordering': ['-assigned_at'], 'verbose_name': 'Group Membership', 'verbose_name_plural': 'Group Memberships'},
        ),
        migrations.RemoveIndex(
            model_name='groupmembership',
            name='staff_group_user_id_2b8aec_idx',
        ),
        migrations.RemoveIndex(
            model_name='groupmembership',
            name='staff_group_group_i_6c86ed_idx',
        ),
        migrations.AlterField(
            model_name='permissionaudit',
            name='action',
            field=models.CharField(choices=[('group_created', 'Group Created'), ('group_updated', 'Group Updated'), ('group_deleted', 'Group Deleted'), ('role_created', 'Role Created'), ('role_updated', 'Role Updated'), ('role_deleted', 'Role Deleted'), ('user_added_to_group', 'User Added to Group'), ('user_removed_from_group', 'User Removed from Group'), ('bulk_users_assigned', 'Bulk Users Assigned'), ('permission_granted', 'Permission Granted'), ('permission_revoked', 'Permission Revoked'), ('permission_added_to_role', 'Permission Added to Role'), ('permission_removed_from_role', 'Permission Removed from Role'), ('staff_user_created', 'Staff User Created'), ('staff_profile_created', 'Staff Profile Created'), ('staff_profile_updated', 'Staff Profile Updated'), ('staff_status_changed', 'Staff Status Changed'), ('user_staff_toggled', 'User Staff Status Toggled'), ('unauthorized_access_attempt', 'Unauthorized Access Attempt'), ('permission_check_failed', 'Permission Check Failed'), ('suspicious_activity', 'Suspicious Activity')], db_index=True, max_length=50),
        ),
        migrations.AddField(
            model_name='staffprofile',
            name='manager',
            field=models.ForeignKey(blank=True, help_text='Direct manager/supervisor', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='direct_reports', to='staff.staffprofile'),
        ),
        migrations.AddField(
            model_name='staffprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='staff_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['department', 'status'], name='staff_staff_departm_d4ebfa_idx'),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['manager', 'status'], name='staff_staff_manager_5798d6_idx'),
        ),
        migrations.AddIndex(
            model_name='staffprofile',
            index=models.Index(fields=['employee_id'], name='staff_staff_employe_e43664_idx'),
        ),
    ]
