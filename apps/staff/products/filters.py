import django_filters
from django.db.models import Q
from apps.products.models import (
    Product, Category, Brand, ProductType, Attribute, AttributeValue,
    ProductVariant, Review, Discount, BrandProductType, ProductTypeAttribute, ProductAttributeValue,
    ProductVariantAttributeValue
)


class ProductStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff product management
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    brand = django_filters.ModelMultipleChoiceFilter(queryset=Brand.objects.all())
    category = django_filters.ModelMultipleChoiceFilter(queryset=Category.objects.all())
    product_type = django_filters.ModelMultipleChoiceFilter(queryset=ProductType.objects.all())
    is_active = django_filters.BooleanFilter()
    is_digital = django_filters.BooleanFilter()

    # Date range filters
    created_after = django_filters.DateFilter(field_name='created_at', lookup_expr='gte')
    created_before = django_filters.DateFilter(field_name='created_at', lookup_expr='lte')
    updated_after = django_filters.DateFilter(field_name='updated_at', lookup_expr='gte')
    updated_before = django_filters.DateFilter(field_name='updated_at', lookup_expr='lte')

    # Rating filters
    min_rating = django_filters.NumberFilter(field_name='average_rating', lookup_expr='gte')
    max_rating = django_filters.NumberFilter(field_name='average_rating', lookup_expr='lte')

    # Stock filters
    has_stock = django_filters.BooleanFilter(method='filter_has_stock')
    low_stock = django_filters.BooleanFilter(method='filter_low_stock')
    out_of_stock = django_filters.BooleanFilter(method='filter_out_of_stock')

    # Variant filters
    has_variants = django_filters.BooleanFilter(method='filter_has_variants')
    has_images = django_filters.BooleanFilter(method='filter_has_images')
    has_reviews = django_filters.BooleanFilter(method='filter_has_reviews')

    class Meta:
        model = Product
        fields = [
            'title', 'brand', 'category', 'product_type', 'is_active',
            'is_digital', 'created_after', 'created_before', 'updated_after',
            'updated_before', 'min_rating', 'max_rating'
        ]

    def filter_has_stock(self, queryset, name, value):
        if value:
            return queryset.filter(product_variant__stock_qty__gt=0).distinct()
        return queryset.filter(product_variant__stock_qty=0).distinct()

    def filter_low_stock(self, queryset, name, value):
        if value:
            return queryset.filter(product_variant__stock_qty__lte=10, product_variant__stock_qty__gt=0).distinct()
        return queryset

    def filter_out_of_stock(self, queryset, name, value):
        if value:
            return queryset.filter(product_variant__stock_qty=0).distinct()
        return queryset

    def filter_has_variants(self, queryset, name, value):
        if value:
            return queryset.filter(product_variant__isnull=False).distinct()
        return queryset.filter(product_variant__isnull=True)

    def filter_has_images(self, queryset, name, value):
        if value:
            return queryset.filter(product_variant__product_image__isnull=False).distinct()
        return queryset.filter(product_variant__product_image__isnull=True).distinct()

    def filter_has_reviews(self, queryset, name, value):
        if value:
            return queryset.filter(reviews__isnull=False).distinct()
        return queryset.filter(reviews__isnull=True)


class CategoryStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff category management
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    parent = django_filters.ModelChoiceFilter(queryset=Category.objects.all())
    level = django_filters.NumberFilter()
    has_children = django_filters.BooleanFilter(method='filter_has_children')
    has_products = django_filters.BooleanFilter(method='filter_has_products')

    class Meta:
        model = Category
        fields = ['title', 'is_active', 'parent', 'level']

    def filter_has_children(self, queryset, name, value):
        if value:
            return queryset.filter(children__isnull=False).distinct()
        return queryset.filter(children__isnull=True)

    def filter_has_products(self, queryset, name, value):
        if value:
            return queryset.filter(products__isnull=False).distinct()
        return queryset.filter(products__isnull=True)


class BrandStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff brand management
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    has_products = django_filters.BooleanFilter(method='filter_has_products')
    product_types = django_filters.ModelMultipleChoiceFilter(queryset=ProductType.objects.all())

    class Meta:
        model = Brand
        fields = ['title', 'is_active']

    def filter_has_products(self, queryset, name, value):
        if value:
            return queryset.filter(product__isnull=False).distinct()
        return queryset.filter(product__isnull=True)


class ProductVariantStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff product variant management
    """
    product = django_filters.ModelChoiceFilter(queryset=Product.objects.all())
    sku = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    condition = django_filters.ChoiceFilter(choices=ProductVariant.CONDITION_CHOICES)

    # Price filters
    min_price = django_filters.NumberFilter(field_name='price', lookup_expr='gte')
    max_price = django_filters.NumberFilter(field_name='price', lookup_expr='lte')

    # Stock filters
    min_stock = django_filters.NumberFilter(field_name='stock_qty', lookup_expr='gte')
    max_stock = django_filters.NumberFilter(field_name='stock_qty', lookup_expr='lte')
    out_of_stock = django_filters.BooleanFilter(method='filter_out_of_stock')
    low_stock = django_filters.BooleanFilter(method='filter_low_stock')

    class Meta:
        model = ProductVariant
        fields = ['product', 'sku', 'is_active', 'condition']

    def filter_out_of_stock(self, queryset, name, value):
        if value:
            return queryset.filter(stock_qty=0)
        return queryset.filter(stock_qty__gt=0)

    def filter_low_stock(self, queryset, name, value):
        if value:
            return queryset.filter(stock_qty__lte=10, stock_qty__gt=0)
        return queryset


class AttributeStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff attribute management
    """
    title = django_filters.CharFilter(lookup_expr='icontains')
    has_values = django_filters.BooleanFilter(method='filter_has_values')
    product_types = django_filters.ModelMultipleChoiceFilter(
        field_name='product_type_attribute__product_type',
        queryset=ProductType.objects.all()
    )

    class Meta:
        model = Attribute
        fields = ['title']

    def filter_has_values(self, queryset, name, value):
        if value:
            return queryset.filter(attribute_value__isnull=False).distinct()
        return queryset.filter(attribute_value__isnull=True)


class AttributeValueStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff attribute value management
    """
    attribute_value = django_filters.CharFilter(lookup_expr='icontains')
    attribute = django_filters.ModelChoiceFilter(queryset=Attribute.objects.all())
    is_active = django_filters.BooleanFilter()
    has_products = django_filters.BooleanFilter(method='filter_has_products')

    class Meta:
        model = AttributeValue
        fields = ['attribute_value', 'attribute', 'is_active']

    def filter_has_products(self, queryset, name, value):
        if value:
            return queryset.filter(
                Q(product_attr_value__isnull=False) |
                Q(product_variant_attribute_value__isnull=False)
            ).distinct()
        return queryset.filter(
            product_attr_value__isnull=True,
            product_variant_attribute_value__isnull=True
        )


class ReviewStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff review management
    """
    product = django_filters.ModelChoiceFilter(queryset=Product.objects.all())
    rating = django_filters.NumberFilter()
    min_rating = django_filters.NumberFilter(field_name='rating', lookup_expr='gte')
    max_rating = django_filters.NumberFilter(field_name='rating', lookup_expr='lte')

    # Date filters
    posted_after = django_filters.DateFilter(field_name='posted_at', lookup_expr='gte')
    posted_before = django_filters.DateFilter(field_name='posted_at', lookup_expr='lte')

    # Text search
    title = django_filters.CharFilter(lookup_expr='icontains')
    description = django_filters.CharFilter(lookup_expr='icontains')

    class Meta:
        model = Review
        fields = ['product', 'rating', 'title', 'description']


class DiscountStaffFilter(django_filters.FilterSet):
    """
    Filter class for staff discount management
    """
    name = django_filters.CharFilter(lookup_expr='icontains')
    is_active = django_filters.BooleanFilter()
    is_valid = django_filters.BooleanFilter(method='filter_is_valid')

    # Date filters
    start_after = django_filters.DateFilter(field_name='start_date', lookup_expr='gte')
    start_before = django_filters.DateFilter(field_name='start_date', lookup_expr='lte')
    end_after = django_filters.DateFilter(field_name='end_date', lookup_expr='gte')
    end_before = django_filters.DateFilter(field_name='end_date', lookup_expr='lte')

    # Percentage filters
    min_discount = django_filters.NumberFilter(field_name='discount_percentage', lookup_expr='gte')
    max_discount = django_filters.NumberFilter(field_name='discount_percentage', lookup_expr='lte')

    class Meta:
        model = Discount
        fields = ['name', 'is_active']

    def filter_is_valid(self, queryset, name, value):
        from django.utils import timezone
        now = timezone.now().date()

        if value:
            return queryset.filter(
                is_active=True,
                start_date__lte=now,
                end_date__gte=now
            )
        return queryset.filter(
            Q(is_active=False) |
            Q(start_date__gt=now) |
            Q(end_date__lt=now)
        )


class BrandProductTypeStaffFilter(django_filters.FilterSet):
    """
    Filter class for brand-product type associations
    """
    brand = django_filters.ModelChoiceFilter(queryset=Brand.objects.all())
    product_type = django_filters.ModelChoiceFilter(queryset=ProductType.objects.all())
    brand_title = django_filters.CharFilter(field_name='brand__title', lookup_expr='icontains')
    product_type_title = django_filters.CharFilter(field_name='product_type__title', lookup_expr='icontains')

    class Meta:
        model = BrandProductType
        fields = ['brand', 'product_type']


class ProductTypeAttributeStaffFilter(django_filters.FilterSet):
    """
    Filter class for product type attribute associations
    """
    product_type = django_filters.ModelChoiceFilter(queryset=ProductType.objects.all())
    attribute = django_filters.ModelChoiceFilter(queryset=Attribute.objects.all())
    is_filterable = django_filters.BooleanFilter()
    is_option_selector = django_filters.BooleanFilter()
    product_type_title = django_filters.CharFilter(field_name='product_type__title', lookup_expr='icontains')
    attribute_title = django_filters.CharFilter(field_name='attribute__title', lookup_expr='icontains')

    class Meta:
        model = ProductTypeAttribute
        fields = ['product_type', 'attribute', 'is_filterable', 'is_option_selector']


class ProductAttributeValueStaffFilter(django_filters.FilterSet):
    """
    Filter class for product attribute value associations
    """
    product = django_filters.ModelChoiceFilter(queryset=Product.objects.all())
    attribute_value = django_filters.ModelChoiceFilter(queryset=AttributeValue.objects.all())
    attribute = django_filters.ModelChoiceFilter(
        field_name='attribute_value__attribute',
        queryset=Attribute.objects.all()
    )
    product_title = django_filters.CharFilter(field_name='product__title', lookup_expr='icontains')
    attribute_title = django_filters.CharFilter(
        field_name='attribute_value__attribute__title',
        lookup_expr='icontains'
    )

    class Meta:
        model = ProductAttributeValue
        fields = ['product', 'attribute_value']


class ProductVariantAttributeValueStaffFilter(django_filters.FilterSet):
    """
    Filter class for product variant attribute value associations
    """
    product_variant = django_filters.ModelChoiceFilter(queryset=ProductVariant.objects.all())
    attribute_value = django_filters.ModelChoiceFilter(queryset=AttributeValue.objects.all())
    attribute = django_filters.ModelChoiceFilter(
        field_name='attribute_value__attribute',
        queryset=Attribute.objects.all()
    )
    is_active = django_filters.BooleanFilter()
    product_title = django_filters.CharFilter(
        field_name='product_variant__product__title',
        lookup_expr='icontains'
    )
    variant_sku = django_filters.CharFilter(field_name='product_variant__sku', lookup_expr='icontains')
    attribute_title = django_filters.CharFilter(
        field_name='attribute_value__attribute__title',
        lookup_expr='icontains'
    )

    class Meta:
        model = ProductVariantAttributeValue
        fields = ['product_variant', 'attribute_value', 'is_active']
