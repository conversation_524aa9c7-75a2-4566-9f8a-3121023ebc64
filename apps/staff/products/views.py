from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count, Sum, Avg
from django.db import transaction
from django.shortcuts import get_object_or_404

from apps.products.models import (
    Product, Category, Brand, ProductType, Attribute, AttributeValue,
    ProductVariant, ProductImage, Review, Discount, ProductTypeAttribute
)
from .models import ProductAudit, BulkProductOperation
from .serializers import (
    ProductStaffSerializer, ProductListStaffSerializer, CategoryStaffSerializer,
    BrandStaffSerializer, ProductTypeStaffSerializer, AttributeStaffSerializer,
    AttributeValueStaffSerializer, ProductVariantStaffSerializer,
    ProductImageStaffSerializer, ReviewStaffSerializer, DiscountStaffSerializer,
    BulkProductSerializer, BulkAttributeAssociationSerializer,
    ProductAuditSerializer, BulkOperationSerializer,
    ProductCreateWithVariantsSerializer, CategoryMoveSerializer,
    ProductAnalyticsSerializer, ProductTypeAttributeStaffSerializer
)
from .permissions import (
    CanManageProducts, CanManageCategories, CanManageBrands,
    CanManageProductTypes, CanManageAttributes, CanManageReviews,
    CanManageDiscounts, IsProductManager, IsInventoryManager
)
from .filters import (
    ProductStaffFilter, CategoryStaffFilter, BrandStaffFilter,
    ProductVariantStaffFilter, AttributeStaffFilter, AttributeValueStaffFilter,
    ReviewStaffFilter, DiscountStaffFilter
)
from .services import ProductService, CategoryService, AuditService
from apps.staff.common.utils import paginate_queryset


class ProductStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product management
    Provides full CRUD operations with role-based permissions
    """
    queryset = Product.objects.all().select_related(
        'brand', 'category', 'product_type'
    ).prefetch_related(
        'product_variant', 'reviews', 'attribute_value'
    )
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductStaffFilter
    search_fields = ['title', 'description', 'brand__title', 'category__title']
    ordering_fields = ['title', 'created_at', 'updated_at', 'average_rating']
    ordering = ['-updated_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductListStaffSerializer
        elif self.action == 'create_with_variants':
            return ProductCreateWithVariantsSerializer
        return ProductStaffSerializer

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add annotations for better performance
        queryset = queryset.annotate(
            variants_count=Count('product_variant'),
            total_stock=Sum('product_variant__stock_qty'),
            reviews_count=Count('reviews')
        )

        return queryset

    def perform_create(self, serializer):
        product = serializer.save()

        # Log the creation
        AuditService.log_product_action(
            product=product,
            staff_user=self.request.user,
            action='CREATE',
            changes=serializer.validated_data,
            request=self.request
        )

    def perform_update(self, serializer):
        # Store original values for audit
        original_values = {}
        for field in serializer.validated_data.keys():
            original_values[field] = getattr(serializer.instance, field)

        product = serializer.save()

        # Log the update
        AuditService.log_product_action(
            product=product,
            staff_user=self.request.user,
            action='UPDATE',
            changes={
                'original': original_values,
                'updated': serializer.validated_data
            },
            request=self.request
        )

    def perform_destroy(self, instance):
        # Log the deletion before actually deleting
        AuditService.log_product_action(
            product=instance,
            staff_user=self.request.user,
            action='DELETE',
            changes={'deleted_product_data': {
                'title': instance.title,
                'id': instance.id
            }},
            request=self.request
        )
        instance.delete()

    @action(detail=False, methods=['post'], permission_classes=[CanManageProducts])
    def bulk_operations(self, request):
        """
        Handle bulk operations on products
        """
        serializer = BulkProductSerializer(data=request.data)
        if serializer.is_valid():
            operation_type = serializer.validated_data['operation_type']
            product_ids = serializer.validated_data['product_ids']
            data = serializer.validated_data.get('data', {})

            try:
                if operation_type == 'update':
                    result = ProductService.bulk_update_products(
                        products_data=[{**data, 'id': pid} for pid in product_ids],
                        staff_user=request.user,
                        request=request
                    )
                elif operation_type == 'activate':
                    Product.objects.filter(id__in=product_ids).update(is_active=True)
                    result = {'updated_count': len(product_ids)}
                elif operation_type == 'deactivate':
                    Product.objects.filter(id__in=product_ids).update(is_active=False)
                    result = {'updated_count': len(product_ids)}
                elif operation_type == 'delete':
                    Product.objects.filter(id__in=product_ids).delete()
                    result = {'deleted_count': len(product_ids)}
                elif operation_type == 'assign_category':
                    category_id = data.get('category_id')
                    Product.objects.filter(id__in=product_ids).update(category_id=category_id)
                    result = {'updated_count': len(product_ids)}
                else:
                    return Response(
                        {'error': 'Invalid operation type'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                return Response(result, status=status.HTTP_200_OK)

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[CanManageProducts])
    def create_with_variants(self, request):
        """
        Create a product with variants in one request
        """
        serializer = ProductCreateWithVariantsSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            result = serializer.save()
            return Response({
                'product': ProductStaffSerializer(result['product']).data,
                'variants': ProductVariantStaffSerializer(result['variants'], many=True).data
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['patch'], permission_classes=[CanManageProducts])
    def change_status(self, request, pk=None):
        """
        Change product active status
        """
        product = self.get_object()
        is_active = request.data.get('is_active')

        if is_active is not None:
            old_status = product.is_active
            product.is_active = is_active
            product.save()

            # Log the status change
            AuditService.log_product_action(
                product=product,
                staff_user=request.user,
                action='STATUS_CHANGE',
                changes={
                    'old_status': old_status,
                    'new_status': is_active
                },
                request=request
            )

            return Response({
                'message': 'Product status updated successfully',
                'is_active': product.is_active
            })

        return Response(
            {'error': 'is_active field is required'},
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=False, methods=['get'], permission_classes=[CanManageProducts])
    def analytics(self, request):
        """
        Get product analytics data
        """
        analytics_data = ProductService.get_product_analytics()

        # Add additional analytics
        analytics_data.update({
            'top_categories': list(
                Category.objects.annotate(
                    products_count=Count('products')
                ).order_by('-products_count')[:5].values('title', 'products_count')
            ),
            'top_brands': list(
                Brand.objects.annotate(
                    products_count=Count('product')
                ).order_by('-products_count')[:5].values('title', 'products_count')
            ),
            'stock_alerts': list(
                ProductVariant.objects.filter(
                    stock_qty__lte=10, stock_qty__gt=0
                ).select_related('product').values(
                    'product__title', 'sku', 'stock_qty'
                )[:10]
            )
        })

        serializer = ProductAnalyticsSerializer(analytics_data)
        return Response(serializer.data)


class CategoryStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff category management
    """
    queryset = Category.objects.all().select_related('parent')
    serializer_class = CategoryStaffSerializer
    permission_classes = [CanManageCategories]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = CategoryStaffFilter
    search_fields = ['title']
    ordering_fields = ['title', 'level']
    ordering = ['tree_id', 'lft']  # MPTT ordering

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('products'),
            children_count=Count('children')
        )

        return queryset

    @action(detail=True, methods=['post'], permission_classes=[CanManageCategories])
    def move(self, request, pk=None):
        """
        Move category to a new parent
        """
        category = self.get_object()
        serializer = CategoryMoveSerializer(data=request.data)

        if serializer.is_valid():
            new_parent_id = serializer.validated_data.get('new_parent_id')

            try:
                moved_category = CategoryService.move_category(
                    category_id=category.id,
                    new_parent_id=new_parent_id,
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': 'Category moved successfully',
                    'category': CategoryStaffSerializer(moved_category).data
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """
        Get category tree structure
        """
        categories = Category.objects.all().order_by('tree_id', 'lft')
        serializer = CategoryStaffSerializer(categories, many=True)
        return Response(serializer.data)


class BrandStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff brand management
    """
    queryset = Brand.objects.all().prefetch_related('product_types')
    serializer_class = BrandStaffSerializer
    permission_classes = [CanManageBrands]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = BrandStaffFilter
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('product')
        )

        return queryset


class ProductTypeStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product type management
    """
    queryset = ProductType.objects.all().select_related('parent').prefetch_related('attribute')
    serializer_class = ProductTypeStaffSerializer
    permission_classes = [CanManageProductTypes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add counts
        queryset = queryset.annotate(
            attributes_count=Count('attribute'),
            products_count=Count('product_type')
        )

        return queryset

    @action(detail=True, methods=['get'], permission_classes=[CanManageProductTypes])
    def attributes(self, request, pk=None):
        """
        Get attributes associated with this product type
        """
        product_type = self.get_object()
        associations = ProductTypeAttribute.objects.filter(
            product_type=product_type
        ).select_related('attribute')

        serializer = ProductTypeAttributeStaffSerializer(associations, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[CanManageProductTypes])
    def associate_attributes(self, request, pk=None):
        """
        Associate attributes with product type
        """
        product_type = self.get_object()
        serializer = BulkAttributeAssociationSerializer(data=request.data)

        if serializer.is_valid():
            try:
                associations = ProductService.associate_attributes_bulk(
                    product_type_id=product_type.id,
                    attribute_data=serializer.validated_data['attributes'],
                    staff_user=request.user,
                    request=request
                )

                return Response({
                    'message': 'Attributes associated successfully',
                    'associations_count': len(associations)
                })

            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AttributeStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff attribute management
    """
    queryset = Attribute.objects.all().prefetch_related('attribute_value', 'product_type_attribute')
    serializer_class = AttributeStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AttributeStaffFilter
    search_fields = ['title']
    ordering_fields = ['title']
    ordering = ['title']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add counts
        queryset = queryset.annotate(
            values_count=Count('attribute_value')
        )

        return queryset


class AttributeValueStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff attribute value management
    """
    queryset = AttributeValue.objects.all().select_related('attribute')
    serializer_class = AttributeValueStaffSerializer
    permission_classes = [CanManageAttributes]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = AttributeValueStaffFilter
    search_fields = ['attribute_value', 'attribute__title']
    ordering_fields = ['attribute_value', 'attribute__title']
    ordering = ['attribute__title', 'attribute_value']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Add product counts
        queryset = queryset.annotate(
            products_count=Count('product_attr_value')
        )

        return queryset

    @action(detail=False, methods=['post'], permission_classes=[CanManageAttributes])
    def bulk_create(self, request):
        """
        Bulk create attribute values
        """
        attribute_id = request.data.get('attribute_id')
        values = request.data.get('values', [])

        if not attribute_id or not values:
            return Response(
                {'error': 'attribute_id and values are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            with transaction.atomic():
                created_values = []
                for value_text in values:
                    value, created = AttributeValue.objects.get_or_create(
                        attribute_id=attribute_id,
                        attribute_value=value_text
                    )
                    if created:
                        created_values.append(value)

                return Response({
                    'message': f'Created {len(created_values)} attribute values',
                    'created_count': len(created_values),
                    'values': AttributeValueStaffSerializer(created_values, many=True).data
                })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ProductVariantStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product variant management
    """
    queryset = ProductVariant.objects.all().select_related(
        'product', 'price_label'
    ).prefetch_related('product_image', 'attribute_value')
    serializer_class = ProductVariantStaffSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ProductVariantStaffFilter
    search_fields = ['sku', 'product__title']
    ordering_fields = ['price', 'stock_qty', 'created_at', 'order']
    ordering = ['product', 'order']

    @action(detail=True, methods=['patch'], permission_classes=[IsInventoryManager])
    def update_stock(self, request, pk=None):
        """
        Update stock quantity (for inventory managers)
        """
        variant = self.get_object()
        new_stock = request.data.get('stock_qty')

        if new_stock is not None:
            old_stock = variant.stock_qty
            variant.stock_qty = new_stock
            variant.save()

            # Log the stock update
            AuditService.log_product_action(
                product=variant.product,
                staff_user=request.user,
                action='VARIANT_UPDATE',
                changes={
                    'variant_sku': variant.sku,
                    'old_stock': old_stock,
                    'new_stock': new_stock
                },
                request=request
            )

            return Response({
                'message': 'Stock updated successfully',
                'sku': variant.sku,
                'stock_qty': variant.stock_qty
            })

        return Response(
            {'error': 'stock_qty is required'},
            status=status.HTTP_400_BAD_REQUEST
        )


class ProductImageStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff product image management
    """
    queryset = ProductImage.objects.all().select_related('product_variant')
    serializer_class = ProductImageStaffSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['order']
    ordering = ['product_variant', 'order']

    def perform_create(self, serializer):
        image = serializer.save()

        # Log the image addition
        AuditService.log_product_action(
            product=image.product_variant.product,
            staff_user=self.request.user,
            action='IMAGE_ADD',
            changes={
                'variant_sku': image.product_variant.sku,
                'image_alt': image.alternative_text
            },
            request=self.request
        )


class ReviewStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff review management (read-only with moderation)
    """
    queryset = Review.objects.all().select_related('product', 'customer')
    serializer_class = ReviewStaffSerializer
    permission_classes = [CanManageReviews]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = ReviewStaffFilter
    search_fields = ['title', 'description', 'product__title']
    ordering_fields = ['rating', 'posted_at']
    ordering = ['-posted_at']
    http_method_names = ['get', 'delete']  # Read-only with delete for moderation

    @action(detail=True, methods=['post'], permission_classes=[CanManageReviews])
    def moderate(self, request, pk=None):
        """
        Moderate review (placeholder for future moderation features)
        """
        review = self.get_object()
        action = request.data.get('action')  # 'approve', 'reject', 'flag'

        # This is a placeholder - you can extend with actual moderation logic
        return Response({
            'message': f'Review {action} action recorded',
            'review_id': review.id
        })


class DiscountStaffViewSet(viewsets.ModelViewSet):
    """
    ViewSet for staff discount management
    """
    queryset = Discount.objects.all().prefetch_related('product_variants')
    serializer_class = DiscountStaffSerializer
    permission_classes = [CanManageDiscounts]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_class = DiscountStaffFilter
    search_fields = ['name']
    ordering_fields = ['name', 'start_date', 'end_date', 'discount_percentage']
    ordering = ['-start_date']

    @action(detail=True, methods=['post'], permission_classes=[CanManageDiscounts])
    def apply_to_variants(self, request, pk=None):
        """
        Apply discount to specific product variants
        """
        discount = self.get_object()
        variant_ids = request.data.get('variant_ids', [])

        if not variant_ids:
            return Response(
                {'error': 'variant_ids is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            variants = ProductVariant.objects.filter(id__in=variant_ids)
            discount.product_variants.add(*variants)

            return Response({
                'message': f'Discount applied to {len(variants)} variants',
                'applied_count': len(variants)
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ProductAuditViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing product audit logs
    """
    queryset = ProductAudit.objects.all().select_related('product', 'staff_user')
    serializer_class = ProductAuditSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['timestamp']
    ordering = ['-timestamp']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by product if specified
        product_id = self.request.query_params.get('product_id')
        if product_id:
            queryset = queryset.filter(product_id=product_id)

        # Filter by staff user if specified
        staff_user_id = self.request.query_params.get('staff_user_id')
        if staff_user_id:
            queryset = queryset.filter(staff_user_id=staff_user_id)

        # Filter by action if specified
        action = self.request.query_params.get('action')
        if action:
            queryset = queryset.filter(action=action)

        return queryset


class BulkOperationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing bulk operation status
    """
    queryset = BulkProductOperation.objects.all().select_related('staff_user')
    serializer_class = BulkOperationSerializer
    permission_classes = [CanManageProducts]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    ordering_fields = ['started_at']
    ordering = ['-started_at']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by staff user if specified
        staff_user_id = self.request.query_params.get('staff_user_id')
        if staff_user_id:
            queryset = queryset.filter(staff_user_id=staff_user_id)

        # Filter by status if specified
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        return queryset


class AssociationManagementViewSet(viewsets.ViewSet):
    """
    ViewSet for managing product type attribute associations
    Replicates the enhanced admin interface functionality
    """
    permission_classes = [CanManageAttributes]

    @action(detail=False, methods=['get'])
    def product_type_attributes(self, request):
        """
        Get product type attributes with search and filtering
        """
        product_type_id = request.query_params.get('product_type_id')
        search = request.query_params.get('search', '')

        if product_type_id:
            # Get existing associations for the product type
            associations = ProductTypeAttribute.objects.filter(
                product_type_id=product_type_id
            ).select_related('attribute')

            if search:
                associations = associations.filter(
                    attribute__title__icontains=search
                )

            serializer = ProductTypeAttributeStaffSerializer(associations, many=True)
            return Response(serializer.data)

        # Return all product type attributes if no specific product type
        associations = ProductTypeAttribute.objects.all().select_related(
            'product_type', 'attribute'
        )

        if search:
            associations = associations.filter(
                Q(product_type__title__icontains=search) |
                Q(attribute__title__icontains=search)
            )

        # Paginate results
        paginated_data = paginate_queryset(associations, request)
        serializer = ProductTypeAttributeStaffSerializer(
            paginated_data['items'], many=True
        )

        return Response({
            'results': serializer.data,
            'pagination': paginated_data['pagination']
        })

    @action(detail=False, methods=['post'])
    def save_association(self, request):
        """
        Save or update product type attribute association
        """
        serializer = ProductTypeAttributeStaffSerializer(data=request.data)

        if serializer.is_valid():
            association = serializer.save()

            # Log the action
            AuditService.log_action(
                action='ASSOCIATION_SAVE',
                staff_user=request.user,
                details={
                    'product_type_id': association.product_type.id,
                    'attribute_id': association.attribute.id,
                    'is_filterable': association.is_filterable,
                    'is_option_selector': association.is_option_selector
                },
                request=request
            )

            return Response(
                ProductTypeAttributeStaffSerializer(association).data,
                status=status.HTTP_201_CREATED
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def delete_association(self, request):
        """
        Delete product type attribute association
        """
        association_id = request.data.get('association_id')

        if not association_id:
            return Response(
                {'error': 'association_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            association = ProductTypeAttribute.objects.get(id=association_id)
            association.delete()

            return Response({
                'message': 'Association deleted successfully'
            })

        except ProductTypeAttribute.DoesNotExist:
            return Response(
                {'error': 'Association not found'},
                status=status.HTTP_404_NOT_FOUND
            )
