import uuid
from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models import Q
from apps.products.models import (
    Product, Category, Brand, ProductType, Attribute, AttributeValue,
    ProductVariant, ProductImage, Review, Discount, ProductTypeAttribute,
    ProductAttributeValue, ProductVariantAttributeValue, BrandProductType
)
from .models import ProductAudit, BulkProductOperation
import logging

logger = logging.getLogger(__name__)


class ProductService:
    """
    Service class for product-related operations
    Handles business logic and complex operations
    """
    
    @staticmethod
    def create_product_with_variants(product_data, variants_data, staff_user, request=None):
        """
        Create a product with variants in a single transaction
        """
        try:
            with transaction.atomic():
                # Create the product
                product = Product.objects.create(**product_data)
                
                # Create variants
                created_variants = []
                for variant_data in variants_data:
                    variant_data['product'] = product
                    variant = ProductVariant.objects.create(**variant_data)
                    created_variants.append(variant)
                
                # Log the action
                AuditService.log_product_action(
                    product=product,
                    staff_user=staff_user,
                    action='CREATE',
                    changes={
                        'product_data': product_data,
                        'variants_count': len(created_variants)
                    },
                    request=request
                )
                
                return product, created_variants
                
        except Exception as e:
            logger.error(f"Error creating product with variants: {str(e)}")
            raise ValidationError(f"Failed to create product: {str(e)}")
    
    @staticmethod
    def bulk_update_products(products_data, staff_user, request=None):
        """
        Handle bulk product updates with audit trail
        """
        operation_id = uuid.uuid4()
        
        # Create bulk operation record
        bulk_op = BulkProductOperation.objects.create(
            operation_id=operation_id,
            staff_user=staff_user,
            operation_type='BULK_UPDATE',
            total_items=len(products_data),
            operation_data={'products': products_data}
        )
        
        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()
            
            updated_products = []
            failed_items = []
            
            with transaction.atomic():
                for item_data in products_data:
                    try:
                        product_id = item_data.pop('id')
                        product = Product.objects.get(id=product_id)
                        
                        # Store original values for audit
                        original_values = {
                            field: getattr(product, field) 
                            for field in item_data.keys()
                        }
                        
                        # Update product
                        for field, value in item_data.items():
                            setattr(product, field, value)
                        product.save()
                        
                        # Log individual update
                        AuditService.log_product_action(
                            product=product,
                            staff_user=staff_user,
                            action='BULK_UPDATE',
                            changes={
                                'original': original_values,
                                'updated': item_data,
                                'bulk_operation_id': str(operation_id)
                            },
                            request=request
                        )
                        
                        updated_products.append(product)
                        bulk_op.processed_items += 1
                        
                    except Exception as e:
                        failed_items.append({
                            'item': item_data,
                            'error': str(e)
                        })
                        bulk_op.failed_items += 1
                        logger.error(f"Failed to update product in bulk: {str(e)}")
            
            # Update operation status
            bulk_op.results = {
                'updated_count': len(updated_products),
                'failed_items': failed_items
            }
            
            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()
            
            return {
                'operation_id': operation_id,
                'updated_products': updated_products,
                'failed_items': failed_items,
                'total_processed': bulk_op.processed_items,
                'total_failed': bulk_op.failed_items
            }
            
        except Exception as e:
            bulk_op.mark_failed(str(e))
            logger.error(f"Bulk update operation failed: {str(e)}")
            raise ValidationError(f"Bulk update failed: {str(e)}")
    
    @staticmethod
    def associate_attributes_bulk(product_type_id, attribute_data, staff_user, request=None):
        """
        Bulk associate attributes to product type
        """
        try:
            with transaction.atomic():
                product_type = ProductType.objects.get(id=product_type_id)
                created_associations = []
                
                for attr_data in attribute_data:
                    attribute_id = attr_data['attribute_id']
                    is_filterable = attr_data.get('is_filterable', False)
                    is_option_selector = attr_data.get('is_option_selector', False)
                    
                    association, created = ProductTypeAttribute.objects.get_or_create(
                        product_type=product_type,
                        attribute_id=attribute_id,
                        defaults={
                            'is_filterable': is_filterable,
                            'is_option_selector': is_option_selector
                        }
                    )
                    
                    if not created:
                        # Update existing association
                        association.is_filterable = is_filterable
                        association.is_option_selector = is_option_selector
                        association.save()
                    
                    created_associations.append(association)
                
                # Log the action
                AuditService.log_action(
                    action='BULK_ATTRIBUTE_ASSOCIATION',
                    staff_user=staff_user,
                    details={
                        'product_type_id': product_type_id,
                        'associations_count': len(created_associations),
                        'attribute_data': attribute_data
                    },
                    request=request
                )
                
                return created_associations
                
        except Exception as e:
            logger.error(f"Error in bulk attribute association: {str(e)}")
            raise ValidationError(f"Failed to associate attributes: {str(e)}")
    
    @staticmethod
    def get_product_analytics(product_id=None, date_range=None):
        """
        Get product analytics data
        """
        queryset = Product.objects.all()
        
        if product_id:
            queryset = queryset.filter(id=product_id)
        
        analytics = {
            'total_products': queryset.count(),
            'active_products': queryset.filter(is_active=True).count(),
            'products_with_variants': queryset.filter(product_variant__isnull=False).distinct().count(),
            'products_with_reviews': queryset.filter(reviews__isnull=False).distinct().count(),
            'average_rating': queryset.aggregate(avg_rating=models.Avg('average_rating'))['avg_rating'] or 0,
        }
        
        return analytics


class CategoryService:
    """
    Service class for category-related operations
    """
    
    @staticmethod
    def move_category(category_id, new_parent_id, staff_user, request=None):
        """
        Move category to a new parent in the tree
        """
        try:
            with transaction.atomic():
                category = Category.objects.get(id=category_id)
                old_parent = category.parent
                
                if new_parent_id:
                    new_parent = Category.objects.get(id=new_parent_id)
                    category.parent = new_parent
                else:
                    category.parent = None
                
                category.save()
                
                # Log the action
                AuditService.log_action(
                    action='CATEGORY_MOVE',
                    staff_user=staff_user,
                    details={
                        'category_id': category_id,
                        'old_parent_id': old_parent.id if old_parent else None,
                        'new_parent_id': new_parent_id
                    },
                    request=request
                )
                
                return category
                
        except Exception as e:
            logger.error(f"Error moving category: {str(e)}")
            raise ValidationError(f"Failed to move category: {str(e)}")


class AuditService:
    """
    Service class for audit trail operations
    """
    
    @staticmethod
    def log_product_action(product, staff_user, action, changes=None, request=None):
        """
        Log product-related actions
        """
        audit_data = {
            'product': product,
            'staff_user': staff_user,
            'action': action,
            'changes': changes or {},
        }
        
        if request:
            audit_data.update({
                'ip_address': SecurityService.get_client_ip(request),
                'user_agent': SecurityService.get_user_agent(request)
            })
        
        return ProductAudit.objects.create(**audit_data)
    
    @staticmethod
    def log_action(action, staff_user, details=None, request=None):
        """
        Log general actions (non-product specific)
        """
        # This could be extended to a general audit log model
        logger.info(f"Action: {action} by {staff_user.email} - Details: {details}")


class SecurityService:
    """
    Service class for security-related operations
    """
    
    @staticmethod
    def get_client_ip(request):
        """
        Get the client IP address from the request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
    
    @staticmethod
    def get_user_agent(request):
        """
        Get the user agent from the request
        """
        return request.META.get('HTTP_USER_AGENT', '')


class ValidationService:
    """
    Service class for data validation
    """
    
    @staticmethod
    def validate_product_data(data):
        """
        Validate product data before creation/update
        """
        errors = {}
        
        # Check required fields
        required_fields = ['title', 'brand', 'product_type', 'category']
        for field in required_fields:
            if field not in data or not data[field]:
                errors[field] = f"{field} is required"
        
        # Validate slug uniqueness if provided
        if 'slug' in data:
            existing = Product.objects.filter(slug=data['slug'])
            if 'id' in data:
                existing = existing.exclude(id=data['id'])
            if existing.exists():
                errors['slug'] = "Product with this slug already exists"
        
        if errors:
            raise ValidationError(errors)
        
        return True
