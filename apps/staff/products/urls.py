# Products domain URLs for staff operations
from django.urls import path, include
from rest_framework.routers import DefaultRouter

# TODO: Import views when implemented
# from .views import ProductViewSet, CategoryViewSet, BrandViewSet

router = DefaultRouter()
# TODO: Register viewsets when implemented
# router.register(r'products', ProductViewSet, basename='staff-products')
# router.register(r'categories', CategoryViewSet, basename='staff-categories')
# router.register(r'brands', BrandViewSet, basename='staff-brands')

app_name = 'products'

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
]
