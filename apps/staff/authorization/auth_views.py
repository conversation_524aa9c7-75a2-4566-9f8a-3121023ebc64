from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from .serializers import AuthUserSerializer
from .permissions import IsStaffUser
from .services import SecurityService, PermissionService

User = get_user_model()


# Note: Core app handles authentication (login/logout)
# This file only contains staff-specific authorization views

class CurrentUserView(APIView):
    """
    Get current authenticated staff user information with groups and permissions
    This view provides staff-specific user data including;
    - User basic information
    - Group memberships
    - Permissions (both direct and inherited from groups)
    Note: The core app handles authentication, this only provides staff-specific data.
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get current staff user information

        Returns:
            Response: JSON containing user data with groups and permissions
        """
        serializer = AuthUserSerializer(request.user)
        return Response({
            'success': True,
            'user': serializer.data
        })


class UserPermissionsView(APIView):
    """
    Get current user's permissions and groups breakdown
    This view provides detailed permission information including;
    - List of user's groups
    - All permissions (direct and inherited)
    - Group-specific permission breakdown
    - Superuser status
    """
    permission_classes = [IsStaffUser]

    def get(self, request):
        """
        Get detailed permission information for current user

        Returns:
            Response: JSON containing detailed permission breakdown
        """
        user = request.user
        permission_service = PermissionService()

        # Get user groups
        groups = permission_service.get_user_groups(user)

        # Get all permissions
        all_permissions = permission_service.get_user_all_permissions(user)

        # Get group-specific permissions
        group_permissions = {}
        for group in user.groups.all():
            group_permissions[group.name] = list(
                group.permissions.values_list('codename', flat=True)
            )

        return Response({
            'success': True,
            'user_id': user.id,
            'email': user.email,
            'is_superuser': user.is_superuser,
            'groups': groups,
            'permissions': all_permissions,
            'group_permissions': group_permissions
        })


class CheckPermissionView(APIView):
    """
    Check if the current user has a specific permission
    This view allows frontend to validate permissions before showing UI elements
    or making API calls that require specific permissions.
    """
    permission_classes = [IsStaffUser]

    def post(self, request):
        """
        Check if the user has specific permission

        Args:
            request: HTTP request containing 'permission' in data

        Returns:
            Response: JSON indicating whether the user has the permission
        """
        permission = request.data.get('permission')

        if not permission:
            return Response({
                'success': False,
                'error': 'Permission parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        permission_service = PermissionService()
        has_permission = permission_service.check_permission(request.user, permission)

        return Response({
            'success': True,
            'permission': permission,
            'has_permission': has_permission
        })


# For backward compatibility, create instances
current_user = CurrentUserView.as_view()
user_permissions = UserPermissionsView.as_view()
check_permission = CheckPermissionView.as_view()
