from django.urls import path, re_path
from .views import (CustomTokenObtainPairView, CustomTokenRefreshView, CustomTokenVerifyView,
                    LogoutView, InitiateRegistrationView, VerifyCodeView, SetPasswordView,
                    CustomLoginView, CurrentUserView, PasswordResetRequestView,
                    PasswordResetConfirmView, ChangePasswordView, ChangePrimaryAuthMethodView,
                    GoogleLoginView, FacebookLoginView, UpdateContactInfo, VerifyUpdateContactInfo)

urlpatterns = [
    # re_path(r'^o/(?P<provider>\S+)/$', CustomProviderAuthView.as_view(), name='social-auth'),
    path('users/login/', CustomLoginView.as_view(), name='login'),
    path('users/me/', CurrentUserView.as_view(), name='current-user'),
    path('users/logout/', LogoutView.as_view()),
    path('jwt/create/', CustomTokenObtainPairView.as_view()),
    path('jwt/refresh/', CustomTokenRefreshView.as_view()),
    path('jwt/verify/', CustomTokenVerifyView.as_view()),
    path('initiate-registration/', InitiateRegistrationView.as_view(), name='initiate-registration'),
    path('verify-code/', VerifyCodeView.as_view(), name='verify-code'),
    path('set-password/', SetPasswordView.as_view(), name='set-password'),

    path('add-contact/initiate/', UpdateContactInfo.as_view(), name='initiate-add-contact'),
    path('add-contact/verify/', VerifyUpdateContactInfo.as_view(), name='verify-add-contact'),

    path('password-reset-request/', PasswordResetRequestView.as_view(), name='password-reset-request'),
    path('password-reset-confirm/', PasswordResetConfirmView.as_view(), name='password-reset-confirm'),
    path('change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('change-primary-auth-method/', ChangePrimaryAuthMethodView.as_view(), name='change-primary-auth-method'),
    
    ## Social Auth Endpoints
    path('google/', GoogleLoginView.as_view(), name='google-login'),
    path('facebook/', FacebookLoginView.as_view(), name='facebook_login'),
    path('facebook/complete/', FacebookLoginView.as_view(), name='facebook_complete')
]
