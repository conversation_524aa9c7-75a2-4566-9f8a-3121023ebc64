from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.conf import settings
from rest_framework.throttling import AnonRateThrottle
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON><PERSON>ica<PERSON>, AuthUser
from rest_framework_simplejwt.tokens import Token
from rest_framework.request import Request
from phonenumber_field.phonenumber import PhoneNumber
from typing import Optional, Tuple

User = get_user_model()


class InitiateRegistrationRateThrottle(AnonRateThrottle):
    """
    Used to limit the number of registration attempts that can be made
    from a single IP address within a specified time period.
    """
    rate = '5/hour'  # Allows 5 requests per hour per IP


class CustomJWTAuthentication(JWTAuthentication):
    """
    By default, JWT Auth class read token from Authorization header.
    This class overrides the default behavior and reads token from cookie
    - If a token is found in the Authorization header, it uses that.
    - Otherwise, it checks for the token in the cookies (specifically from the 'access_token' cookie).
    """

    def authenticate(self, request: Request) -> Optional[Tuple[AuthUser, Token]]:
        header = self.get_header(request)
        if header is None:
            raw_token = request.COOKIES.get(settings.AUTH_COOKIE)
        else:
            raw_token = self.get_raw_token(header)

        if raw_token is None:
            return None

        validated_token = self.get_validated_token(raw_token)
        return self.get_user(validated_token), validated_token


# This is used in setPasswordView after registering a user
class UserRegistrationJWTAuthentication(JWTAuthentication):
    def authenticate(self, request: Request) -> Optional[Tuple[AuthUser, Token]]:
        raw_token = self._get_token_from_request(request)

        if raw_token is None:
            return None

        try:
            validated_token = self.get_validated_token(raw_token)
            return self.get_user(validated_token), validated_token
        except InvalidToken:
            # Handle invalid token (optional)
            raise AuthenticationFailed('Invalid token')
        except Exception as e:
            # Catch any other token validation errors
            raise AuthenticationFailed(f'Authentication failed: {str(e)}')

    def _get_token_from_request(self, request: Request) -> Optional[str]:
        # Extract the token from the Authorization header or the 'reg_access_token' cookie.
        header = self.get_header(request)
        if header:
            return self.get_raw_token(header)

        # If no token in header, fallback to cookie
        return request.COOKIES.get('short_access_token')


# This custom backend is not used at the moment
class EmailOrPhoneBackend(ModelBackend):
    # Custom authentication backend that allows users to authenticate using either email or phone number.
    def authenticate(self, request, username=None, password=None, **kwargs):
        # Check if the username is a valid email or phone number
        try:
            # Try to interpret 'username' as an email first
            user = User.objects.get(email__iexact=username)
        except User.DoesNotExist:
            try:
                # If email check fails, check if it's a phone number
                # Here we're assuming you're using PhoneNumberField in your User model
                phone_number = PhoneNumber.from_string(phone_number=username)
                user = User.objects.get(phone_number=phone_number)
            except (User.DoesNotExist, ValidationError):
                return None

        # If the user exists, check if the password is correct
        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        return None
